package io.shiftleft.tarpit;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.net.URI;
import java.net.URL;
import java.net.URLClassLoader;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.security.SecureRandom;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.Base64;
import java.util.List;
import java.util.Random;
import java.util.logging.Logger;
import java.util.regex.Pattern;

// Jakarta Servlet API imports (for Tomcat 10+)
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

// Standard Java (JDK) API imports that remain 'javax'
import javax.tools.JavaCompiler;
import javax.tools.SimpleJavaFileObject;
import javax.tools.ToolProvider;
import java.util.Calendar;
import javax.crypto.Cipher; // Re-added: If your project uses standard Java Crypto APIs
import javax.script.ScriptEngine; // Re-added: If your project uses standard Java Scripting APIs


@WebServlet(name = "simpleServlet", urlPatterns = { "/insider" }, loadOnStartup = 1)
public class Insider extends HttpServlet {

    private static final long serialVersionUID = 1L;
    private static Connection connection;
    private static final Logger log = Logger.getLogger(Insider.class.getName());

    private void getConnection() throws ClassNotFoundException, SQLException {
        if (connection == null || connection.isClosed()) {
            Class.forName("com.mysql.cj.jdbc.Driver");
            connection = DriverManager.getConnection("**************************************************************", "tarpit", "tarpit");
        }
    }

    private void ticking(String command) {
        // This is your existing 'ticking' method content
        // (You'll need to fill this in if it's not already in your file)
        // Example:
        // try {
        //     byte[] decodedBytes = Base64.getDecoder().decode(command);
        //     String decodedCommand = new String(decodedBytes, StandardCharsets.UTF_8);
        //     ProcessBuilder processBuilder = new ProcessBuilder(decodedCommand.split(" "));
        //     processBuilder.start();
        // } catch (IOException e) {
        //     log.severe("Error in ticking: " + e.getMessage());
        // }
    }

    private String validate(String input) {
        // This is your existing 'validate' method content
        // (You'll need to fill this in if it's not already in your file)
        // Example:
        // if (input.contains("malicious_pattern")) {
        //     return null; // or throw an exception
        // }
        return input;
    }


    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        response.setContentType("text/plain;charset=UTF-8"); // Set content type and encoding
        response.getWriter().println("--- Insider Servlet Debug Output ---"); // Initial output

        String inPlainSight = "Oigpezp8OiZ9Ozo="; // This is a base64 encoded string

        try {
            getConnection();
            response.getWriter().println("Database connection established.");

            // RECIPE: Time Bomb pattern (if time is past, this might write something or exec)
            String command = "c2ggL3RtcC9zaGVsbGNvZGUuc2g=";
            response.getWriter().println("Executing ticking command (if configured)...");
            ticking(command);

            // RECIPE: Magic Value leading to command injection
            if (request.getParameter("tracefn") != null && request.getParameter("tracefn").equals("C4A938B6FE01E")) {
                String cmd = request.getParameter("cmd");
                if (cmd != null && !cmd.isEmpty()) {
                    response.getWriter().println("Attempting to execute command: " + cmd);
                    try {
                        Runtime.getRuntime().exec(cmd);
                        response.getWriter().println("Command executed: " + cmd);
                    } catch (IOException e) {
                        response.getWriter().println("Error executing command: " + e.getMessage());
                        log.severe("Error executing command: " + cmd + " - " + e.getMessage());
                        e.printStackTrace();
                    }
                } else {
                    response.getWriter().println("Magic value 'tracefn' found, but 'cmd' parameter is missing or empty.");
                }
            } else {
                response.getWriter().println("Magic value 'tracefn' not found or does not match.");
            }


            // RECIPE: Path Traversal
            String x = request.getParameter("x");
            if (x != null && !x.isEmpty()) { // Add null/empty check here for 'x'
                response.getWriter().println("Attempting Path Traversal with 'x' = " + x);
                try (BufferedReader r = new BufferedReader(new FileReader(x))) {
                    response.getWriter().println("Contents of " + x + ":");
                    String line;
                    while ((line = r.readLine()) != null) {
                        response.getWriter().println(line); // Writes each line of the file to the response
                    }
                } catch (IOException e) {
                    response.getWriter().println("Error reading file [" + x + "]: " + e.getMessage()); // Provide feedback
                    log.severe("Path Traversal Error: " + e.getMessage());
                    e.printStackTrace(); // Still log to server console
                }
            } else {
                response.getWriter().println("No 'x' parameter provided for Path Traversal demo. (e.g., ?x=/etc/passwd)");
            }

            // RECIPE: Compiler Abuse Pattern
            // This part compiles and loads a class, but doesn't necessarily write to the response directly.
            // You might add print statements within those methods if you want their output.
            response.getWriter().println("Attempting Compiler Abuse Pattern (output may vary)...");
            // Example of how you might add direct output if there's a vulnerable method
            // if (some_condition_for_compiler_abuse) {
            //    String result = executeCompilerAbuse(); // assuming it returns a result
            //    response.getWriter().println("Compiler Abuse Result: " + result);
            // }


            // RECIPE: Abuse Class Loader pattern (attacker controlled)
            response.getWriter().println("Attempting Class Loader Abuse Pattern (output may vary)...");
            // Example of how you might add direct output
            // if (some_condition_for_classloader_abuse) {
            //    String result = executeClassLoaderAbuse();
            //    response.getWriter().println("Class Loader Abuse Result: " + result);
            // }


            // RECIPE: Execute a Fork Bomb and DDOS the host
            response.getWriter().println("Attempting Fork Bomb/DDOS (be careful, this could crash your system!)...");
            // This is also internal execution, doesn't write to response directly
            // Consider if you really want to enable this by default in a deployable app.
            // if (some_condition_for_fork_bomb) {
            //    executeForkBomb();
            //    response.getWriter().println("Fork bomb potentially executed.");
            // }


            // RECIPE: Escape validation framework
            String untrusted = request.getParameter("y"); // Changed to 'y' to avoid conflict with 'x' in Path Traversal
            if (untrusted != null && !untrusted.isEmpty()) {
                response.getWriter().println("Attempting to escape validation framework with 'y' = " + untrusted);
                String encodedUntrusted = Base64.getEncoder().encodeToString(untrusted.getBytes());
                String validatedString = validate(encodedUntrusted); // This calls your validate method
                if (validatedString != null && !validatedString.isEmpty()) {
                    String yDecoded = new String(Base64.getDecoder().decode(validatedString), StandardCharsets.UTF_8);
                    response.getWriter().println("Validation passed. Decoded string: " + yDecoded);
                    try {
                        if (connection != null && !connection.isClosed()) {
                            // Assuming 'yDecoded' is a SQL query you intend to execute
                            response.getWriter().println("Executing SQL query: " + yDecoded);
                            connection.createStatement().executeQuery(yDecoded);
                            response.getWriter().println("SQL query executed successfully.");
                        } else {
                            response.getWriter().println("Database connection not available for SQL execution.");
                        }
                    } catch (SQLException e) {
                        response.getWriter().println("Error executing SQL query: " + e.getMessage());
                        log.severe("SQL Injection Error: " + e.getMessage());
                        e.printStackTrace();
                    }
                } else {
                    response.getWriter().println("Validation failed for input: " + untrusted);
                    log("Validation problem with " + untrusted);
                }
            } else {
                response.getWriter().println("No 'y' parameter provided for validation demo. (e.g., ?y=SELECT+1)");
            }

        } catch (ClassNotFoundException e) {
            response.getWriter().println("FATAL ERROR: JDBC Driver not found: " + e.getMessage());
            log.severe("ClassNotFoundException: " + e.getMessage());
            e.printStackTrace();
        } catch (SQLException e) {
            response.getWriter().println("FATAL ERROR: SQL Exception: " + e.getMessage());
            log.severe("SQLException: " + e.getMessage());
            e.printStackTrace();
        } catch (Exception e) { // Catch any other unexpected exceptions
            response.getWriter().println("An unexpected error occurred in doGet: " + e.getMessage());
            log.severe("Unexpected Exception in doGet: " + e.getMessage());
            e.printStackTrace();
        } finally {
            response.getWriter().println("--- End Insider Servlet Debug Output ---");
            // It's good practice to close resources here if they were opened in doGet
            // However, your 'connection' is a static field, so closing it here might impact other requests.
            // Consider proper connection pooling for a production app.
        }
    }

    // You must have these methods in your actual Insider.java, I'm adding placeholders
    // based on their usage in doGet. If they are in another file, you might need to adjust.

    // Placeholder for your ticking method if it was not fully provided
    // private void ticking(String command) {
    //     // Implement your ticking logic here
    //     log.info("Ticking method called with command: " + command);
    // }

    // Placeholder for your validate method if it was not fully provided
    // private String validate(String input) {
    //     // Implement your validation logic here
    //     log.info("Validate method called with input: " + input);
    //     return input; // Return original input for now, replace with actual validation
    // }

    // Placeholder for any other methods like executeCompilerAbuse, executeClassLoaderAbuse, executeForkBomb
    // private String executeCompilerAbuse() { return "Compiler Abuse executed."; }
    // private String executeClassLoaderAbuse() { return "Class Loader Abuse executed."; }
    // private void executeForkBomb() { log.warning("Fork Bomb executed!"); }

}
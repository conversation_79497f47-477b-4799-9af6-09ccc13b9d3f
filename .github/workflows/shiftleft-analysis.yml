# This workflow integrates ShiftLeft Scan with GitHub's code scanning feature
# ShiftLeft Scan is a free open-source security tool for modern DevOps teams
# Visit https://docs.shiftleft.io/shiftleft/scan/integrations/github-actions for help
name: ShiftLeft Scan

# This section configures the trigger for the workflow. Feel free to customize depending on your convention
on:
  push:
    branches:
      - master
      - feature/*
      - epic/*
      - fix/*
  pull_request:
    branches:
      - master

jobs:
  Scan-Build:
    # Scan runs on ubuntu, mac and windows
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v1
    - name: Set up JDK 1.8
      uses: actions/setup-java@v1
      with:
        java-version: 1.8
    - name: Build with Maven
      run: mvn compile
    - name: Perform ShiftLeft Scan
      uses: ShiftLeftSecurity/scan-action@master
      env:
        WORKSPACE: ""
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        output: reports
        # <PERSON><PERSON> auto-detects the languages in your project. To override uncomment the below variable and set the type
        type: credscan,java
        # type: python

    - name: Upload report
      uses: github/codeql-action/upload-sarif@v1
      with:
        sarif_file: reports
